.topic-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.bookmarks-header {
  width: 280px;
  padding: 12px 16px;
  background-color: #111111;
  border-radius: 10px;
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  color: #FAFAFA;
  gap: 24px;
  text-align: center;
}


.sidebar-header h5 {
  font-weight: 600;
  font-size: 18px;
  line-height: 100%;
  color: #333;
  margin: 0;

}

.topics-list {
  flex: 1;
  overflow-y: auto;
}

.topic-item {
  border-bottom: none;
  margin-bottom: 24px;
}

.topic-header {
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #333;
}

.topic-header:hover {
  background-color: transparent;
}

.topic-header.active {
  background-color: transparent;
  border-left: none;
}

.topic-title {
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  color: #495057;
}

.topic-header:hover .topic-title {
  color: #495057;
}

.expand-icon {
  font-size: 12px;
  color: #333;
  transition: transform 0.2s ease;
  width: 22px;
  display: inline-block;
  margin-left: auto;
  margin-right: 0;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.subtopics-list {
  background-color: transparent;
  border-top: none;
  margin-top: 12px;
  margin-left: 16px;
}

.subtopic-item {
  padding: 5px 0;
  cursor: pointer;
  transition: color 0.2s ease;
  border-left: none;
  border-bottom: none;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.subtopic-item:hover {
  background-color: transparent;
}

.subtopic-item.active {
  background-color: transparent;
  border-left: none;
}

.subtopic-bullet {
  width: 4px;
  height: 4px;
  background-color: #666;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.subtopic-item.active .subtopic-bullet {
  background-color: #666;
  /* SAME COLOR AS NORMAL BULLETS */
}

.subtopic-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.subtopic-title {
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  color: #666;
}

.subtopic-item:hover .subtopic-title {
  color: #666;
  /* NO COLOR CHANGE ON HOVER */
}

.subtopic-item.active .subtopic-title {
  font-weight: 600;
  font-size: 15px;
  line-height: 100%;
  letter-spacing: 0%;
  text-decoration: underline;
  text-decoration-style: solid;
  text-decoration-thickness: 0%;
  text-decoration-skip-ink: auto;
  color: #333;
}

.page-indicator {
  font-size: 12px;
  color: #999;
  font-weight: 400;
  font-style: normal;
}

/* Additional topics styling */
.additional-topics {
  border-top: 1px solid #e9ecef;
  margin-top: 24px;
  padding-top: 24px;
}

.additional-topics .topic-header {
  background-color: transparent;
}

.additional-topics .subtopic-item {
  background-color: transparent;
}

.additional-topics .subtopic-item:hover {
  background-color: transparent;
}

/* Scrollbar styling */
.topics-list::-webkit-scrollbar {
  width: 6px;
}

.topics-list::-webkit-scrollbar-track {
  background: transparent;
}

.topics-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.topics-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.divider {
  border: 1px solid #C3BFB6;
  width: 288px;
  border-width: 1px;

}