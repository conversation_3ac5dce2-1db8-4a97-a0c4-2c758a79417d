import { Injectable, signal, PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable } from 'rxjs';
import {
  Course,
  Topic,
  SubTopic,
  CoursePage,
  Highlight,
  Bookmark,
  Note,
  AudioItem,
  UserPreferences
} from '../models/course.model';

@Injectable({
  providedIn: 'root'
})
export class CourseService {
  private platformId = inject(PLATFORM_ID);
  private isBrowser = isPlatformBrowser(this.platformId);
  private courseSubject = new BehaviorSubject<Course | null>(null);
  private selectedTopicSubject = new BehaviorSubject<Topic | null>(null);
  private selectedSubTopicSubject = new BehaviorSubject<SubTopic | null>(null);
  private currentPageSubject = new BehaviorSubject<CoursePage | null>(null);
  private highlightsSubject = new BehaviorSubject<Highlight[]>([]);
  private bookmarksSubject = new BehaviorSubject<Bookmark[]>([]);
  private notesSubject = new BehaviorSubject<Note[]>([]);
  private userPreferencesSubject = new BehaviorSubject<UserPreferences>({
    textSize: 'normal',
    highlightColor: '#ffff99'
  });

  // Signals for reactive state management
  course = signal<Course | null>(null);
  selectedTopic = signal<Topic | null>(null);
  selectedSubTopic = signal<SubTopic | null>(null);
  currentPage = signal<CoursePage | null>(null);
  highlights = signal<Highlight[]>([]);
  bookmarks = signal<Bookmark[]>([]);
  notes = signal<Note[]>([]);
  userPreferences = signal<UserPreferences>({
    textSize: 'normal',
    highlightColor: '#ffff99'
  });

  course$ = this.courseSubject.asObservable();
  selectedTopic$ = this.selectedTopicSubject.asObservable();
  selectedSubTopic$ = this.selectedSubTopicSubject.asObservable();
  currentPage$ = this.currentPageSubject.asObservable();
  highlights$ = this.highlightsSubject.asObservable();
  bookmarks$ = this.bookmarksSubject.asObservable();
  notes$ = this.notesSubject.asObservable();
  userPreferences$ = this.userPreferencesSubject.asObservable();

  constructor() {
    this.loadMockData();
    this.loadUserData();
  }

  private loadMockData() {
    const mockCourse: Course = {
      id: '1',
      title: 'Physics - Mechanics',
      topics: [
        {
          id: 'dynamics',
          title: 'Dynamics',
          currentPage: 1,
          expanded: true,
          subTopics: [
            {
              id: 'rectilinear-motion',
              title: 'Rectilinear motion',
              currentPage: 1,
              pages: [
                {
                  id: 'page-1',
                  pageNumber: 1,
                  title: 'Kinematics of Particle',
                  audioUrl: '/assets/audio/rectilinear-motion.mp3',
                  contents: [
                    {
                      id: 'content-1',
                      type: 'text',
                      order: 1,
                      content: `<h3>Kinematics of Particle</h3>
                      <p>It is the study of bodies in motion, it is divided into two parts</p>
                      <p><strong>1. Kinematics</strong> (study of geometry of motion) -</p>
                      <p>It is the study of relation between displacement (s), velocity (v), acceleration (a) and time (t).</p>
                      <p><strong>2. Kinetics</strong> -</p>
                      <p>It is the study of relation between force (f), mass (m), displacement (s), velocity (v), acceleration (a) and time (t).</p>

                      <p><strong>Types of motion based on geometry</strong> -</p>
                      <p><strong>1. Translation</strong> -</p>
                      <p>During the motion of translation, orientation of a body does not change. Translation is of two types :</p>
                      <ul>
                        <li><strong>Rectilinear translation.</strong></li>
                        <li><strong>Curvilinear translation.</strong></li>
                      </ul>


                      <div class="diagram-container">
                        <div class="diagram-section">
                          <h4 class="diagram-title">Rectilinear translation</h4>
                          <div class="diagram-content">
                            <div class="shape-box">
                              <span class="corner-label top-left">A</span>
                              <span class="corner-label top-right">B</span>
                              <span class="corner-label bottom-left">C</span>
                              <span class="corner-label bottom-right">D</span>
                            </div>
                            <div class="arrow-right">→</div>
                            <div class="shape-box">
                              <span class="corner-label top-left">A'</span>
                              <span class="corner-label top-right">B'</span>
                              <span class="corner-label bottom-left">C'</span>
                              <span class="corner-label bottom-right">D'</span>
                            </div>
                          </div>
                        </div>

                        <div class="diagram-section">
                          <h4 class="diagram-title">Curvilinear translation</h4>
                          <div class="diagram-content">
                            <div class="shape-box">
                              <span class="corner-label top-left">A</span>
                              <span class="corner-label top-right">B</span>
                              <span class="corner-label bottom-left">C</span>
                              <span class="corner-label bottom-right">D</span>
                            </div>
                            <div class="arrow-curved">⤴</div>
                            <div class="shape-box rotated">
                              <span class="corner-label top-left">A'</span>
                              <span class="corner-label top-right">B'</span>
                              <span class="corner-label bottom-left">C'</span>
                              <span class="corner-label bottom-right">D'</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <p><strong>2. Rotation</strong> -</p>
                      <p>During the motion of rotation, <span style="background-color: #ffff99; padding: 1px 2px; border-radius: 2px;">all the particles will move along concentric circles</span>.</p>`
                    }
                  ]
                },
                {
                  id: 'page-2',
                  pageNumber: 2,
                  title: 'Types of Motion',
                  audioUrl: '/assets/audio/rectilinear-motion.mp3',
                  contents: [
                    {
                      id: 'content-2',
                      type: 'text',
                      order: 1,
                      content: `<h3>Types of Motion</h3>
                      <p>Motion can be classified in various ways based on different criteria:</p>

                      <p><strong>Based on Path:</strong></p>
                      <ul>
                        <li><strong>Linear Motion:</strong> Motion along a straight line</li>
                        <li><strong>Circular Motion:</strong> Motion along a circular path</li>
                        <li><strong>Random Motion:</strong> Motion with no fixed pattern</li>
                      </ul>

                      <p><strong>Based on Nature:</strong></p>
                      <ul>
                        <li><strong>Uniform Motion:</strong> Motion with constant velocity</li>
                        <li><strong>Non-uniform Motion:</strong> Motion with changing velocity</li>
                      </ul>

                      <p>Understanding these classifications helps in analyzing different types of mechanical systems and their behavior.</p>`
                    }
                  ]
                },
                {
                  id: 'page-3',
                  pageNumber: 3,
                  title: 'Equations of Motion',
                  audioUrl: '/assets/audio/rectilinear-motion.mp3',
                  contents: [
                    {
                      id: 'content-3',
                      type: 'text',
                      order: 1,
                      content: `<h3>Equations of Motion</h3>
                      <p>For uniformly accelerated motion, we have three fundamental equations:</p>

                      <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <p><strong>First Equation:</strong> v = u + at</p>
                        <p><strong>Second Equation:</strong> s = ut + ½at²</p>
                        <p><strong>Third Equation:</strong> v² = u² + 2as</p>
                      </div>

                      <p>Where:</p>
                      <ul>
                        <li>u = initial velocity</li>
                        <li>v = final velocity</li>
                        <li>a = acceleration</li>
                        <li>t = time</li>
                        <li>s = displacement</li>
                      </ul>`
                    }
                  ]
                },
                {
                  id: 'page-4',
                  pageNumber: 4,
                  title: 'Applications',
                  audioUrl: '/assets/audio/rectilinear-motion.mp3',
                  contents: [
                    {
                      id: 'content-4',
                      type: 'text',
                      order: 1,
                      content: `<h3>Applications of Rectilinear Motion</h3>
                      <p>Rectilinear motion principles are applied in various engineering fields:</p>

                      <p><strong>Automotive Engineering:</strong></p>
                      <ul>
                        <li>Vehicle acceleration and braking systems</li>
                        <li>Crash test analysis</li>
                        <li>Engine piston motion</li>
                      </ul>

                      <p><strong>Aerospace Engineering:</strong></p>
                      <ul>
                        <li>Rocket launch trajectories</li>
                        <li>Aircraft takeoff and landing</li>
                        <li>Satellite orbital mechanics</li>
                      </ul>

                      <p><strong>Civil Engineering:</strong></p>
                      <ul>
                        <li>Elevator design</li>
                        <li>Bridge dynamics</li>
                        <li>Structural vibration analysis</li>
                      </ul>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'projectile-motion',
              title: 'Projectile motion',
              currentPage: 1,
              pages: [
                {
                  id: 'page-5',
                  pageNumber: 1,
                  title: 'Projectile Motion Basics',
                  contents: [
                    {
                      id: 'content-5',
                      type: 'text',
                      order: 1,
                      content: `<h3>Projectile Motion</h3>
                      <p>Projectile motion is the motion of an object thrown or projected into the air, subject only to acceleration due to gravity.</p>

                      <p><strong>Key Characteristics:</strong></p>
                      <ul>
                        <li>Two-dimensional motion</li>
                        <li>Constant horizontal velocity</li>
                        <li>Uniformly accelerated vertical motion</li>
                        <li>Parabolic trajectory</li>
                      </ul>

                      <p><strong>Components of Projectile Motion:</strong></p>
                      <p><strong>Horizontal Component:</strong> vₓ = v₀ cos θ</p>
                      <p><strong>Vertical Component:</strong> vᵧ = v₀ sin θ - gt</p>

                      <p>Where v₀ is initial velocity, θ is launch angle, and g is acceleration due to gravity.</p>`
                    }
                  ]
                },
                {
                  id: 'page-6',
                  pageNumber: 2,
                  title: 'Trajectory Analysis',
                  contents: [
                    {
                      id: 'content-6',
                      type: 'text',
                      order: 1,
                      content: `<h3>Trajectory Analysis</h3>
                      <p>The path followed by a projectile is called its trajectory, which is always parabolic in nature.</p>

                      <p><strong>Important Parameters:</strong></p>
                      <ul>
                        <li><strong>Range (R):</strong> R = (v₀² sin 2θ)/g</li>
                        <li><strong>Maximum Height (H):</strong> H = (v₀² sin² θ)/(2g)</li>
                        <li><strong>Time of Flight (T):</strong> T = (2v₀ sin θ)/g</li>
                      </ul>

                      <p><strong>Optimal Launch Angle:</strong></p>
                      <p>For maximum range on level ground, the optimal launch angle is 45°.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'tangential-normal',
              title: 'Tangential and normal components',
              currentPage: 1,
              pages: [
                {
                  id: 'page-7',
                  pageNumber: 1,
                  title: 'Tangential and Normal Components',
                  contents: [
                    {
                      id: 'content-7',
                      type: 'text',
                      order: 1,
                      content: `<h3>Tangential and Normal Components</h3>
                      <p>In curvilinear motion, acceleration can be resolved into two components:</p>

                      <p><strong>Tangential Component (aₜ):</strong></p>
                      <ul>
                        <li>Acts along the tangent to the path</li>
                        <li>Responsible for change in speed</li>
                        <li>aₜ = dv/dt</li>
                      </ul>

                      <p><strong>Normal Component (aₙ):</strong></p>
                      <ul>
                        <li>Acts perpendicular to the path</li>
                        <li>Responsible for change in direction</li>
                        <li>aₙ = v²/ρ (where ρ is radius of curvature)</li>
                      </ul>

                      <p>Total acceleration: a = √(aₜ² + aₙ²)</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'motion-fill',
              title: 'Motion Fill (28) + 36 Hua',
              currentPage: 1,
              pages: [
                {
                  id: 'page-8',
                  pageNumber: 1,
                  title: 'Motion Fill',
                  contents: [
                    {
                      id: 'content-8',
                      type: 'text',
                      order: 1,
                      content: `<h3>Motion Fill</h3>
                      <p>Advanced motion analysis techniques and applications.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'kinetics-rectilinear',
              title: 'Kinetics of rectilinear motion',
              currentPage: 1,
              pages: [
                {
                  id: 'page-9',
                  pageNumber: 1,
                  title: 'Kinetics of Rectilinear Motion',
                  contents: [
                    {
                      id: 'content-9',
                      type: 'text',
                      order: 1,
                      content: `<h3>Kinetics of Rectilinear Motion</h3>
                      <p>Study of forces and their effects on rectilinear motion.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'kinetics-curvilinear',
              title: 'Kinetics of curvilinear Motion',
              currentPage: 1,
              pages: [
                {
                  id: 'page-10',
                  pageNumber: 1,
                  title: 'Kinetics of Curvilinear Motion',
                  contents: [
                    {
                      id: 'content-10',
                      type: 'text',
                      order: 1,
                      content: `<h3>Kinetics of Curvilinear Motion</h3>
                      <p>Analysis of forces in curvilinear motion systems.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'work-energy',
              title: 'Work energy',
              currentPage: 1,
              pages: [
                {
                  id: 'page-11',
                  pageNumber: 1,
                  title: 'Work Energy',
                  contents: [
                    {
                      id: 'content-11',
                      type: 'text',
                      order: 1,
                      content: `<h3>Work Energy</h3>
                      <p>Work-energy theorem and its applications in mechanics.</p>`
                    }
                  ]
                }
              ]
            },
            {
              id: 'impulse-momentum',
              title: 'Impulse momentum and impact',
              currentPage: 1,
              pages: [
                {
                  id: 'page-12',
                  pageNumber: 1,
                  title: 'Impulse Momentum and Impact',
                  contents: [
                    {
                      id: 'content-12',
                      type: 'text',
                      order: 1,
                      content: `<h3>Impulse Momentum and Impact</h3>
                      <p>Conservation of momentum and impact analysis.</p>`
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    };

    this.course.set(mockCourse);
    this.courseSubject.next(mockCourse);
    
    // Set initial selections
    if (mockCourse.topics.length > 0) {
      const dynamicsTopic = mockCourse.topics[0];
      dynamicsTopic.expanded = true; // Make sure Dynamics is expanded
      this.selectTopic(dynamicsTopic);

      // Select the first subtopic (Rectilinear motion)
      if (dynamicsTopic.subTopics && dynamicsTopic.subTopics.length > 0) {
        this.selectSubTopic(dynamicsTopic.subTopics[0]);
      }
    }
  }

  private loadUserData() {
    if (!this.isBrowser) return;

    // Load from localStorage or API
    const savedHighlights = localStorage.getItem('highlights');
    const savedBookmarks = localStorage.getItem('bookmarks');
    const savedNotes = localStorage.getItem('notes');
    const savedPreferences = localStorage.getItem('userPreferences');

    if (savedHighlights) {
      const highlights = JSON.parse(savedHighlights);
      this.highlights.set(highlights);
      this.highlightsSubject.next(highlights);
    }

    if (savedBookmarks) {
      const bookmarks = JSON.parse(savedBookmarks);
      this.bookmarks.set(bookmarks);
      this.bookmarksSubject.next(bookmarks);
    }

    if (savedNotes) {
      const notes = JSON.parse(savedNotes);
      this.notes.set(notes);
      this.notesSubject.next(notes);
    }

    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      this.userPreferences.set(preferences);
      this.userPreferencesSubject.next(preferences);
    }
  }

  selectTopic(topic: Topic) {
    this.selectedTopic.set(topic);
    this.selectedTopicSubject.next(topic);
    
    if (topic.subTopics && topic.subTopics.length > 0) {
      this.selectSubTopic(topic.subTopics[0]);
    } else if (topic.pages && topic.pages.length > 0) {
      this.selectPage(topic.pages[topic.currentPage - 1]);
    }
  }

  selectSubTopic(subTopic: SubTopic) {
    this.selectedSubTopic.set(subTopic);
    this.selectedSubTopicSubject.next(subTopic);
    
    if (subTopic.pages && subTopic.pages.length > 0) {
      this.selectPage(subTopic.pages[subTopic.currentPage - 1]);
    }
  }

  selectPage(page: CoursePage) {
    this.currentPage.set(page);
    this.currentPageSubject.next(page);
  }

  // Highlight management
  addHighlight(highlight: Highlight) {
    const currentHighlights = this.highlights();
    const newHighlights = [...currentHighlights, highlight];
    this.highlights.set(newHighlights);
    this.highlightsSubject.next(newHighlights);
    if (this.isBrowser) {
      localStorage.setItem('highlights', JSON.stringify(newHighlights));
    }
  }

  removeHighlight(highlightId: string) {
    const currentHighlights = this.highlights();
    const newHighlights = currentHighlights.filter(h => h.id !== highlightId);
    this.highlights.set(newHighlights);
    this.highlightsSubject.next(newHighlights);
    if (this.isBrowser) {
      localStorage.setItem('highlights', JSON.stringify(newHighlights));
    }
  }

  // Bookmark management
  addBookmark(bookmark: Bookmark) {
    const currentBookmarks = this.bookmarks();
    const newBookmarks = [...currentBookmarks, bookmark];
    this.bookmarks.set(newBookmarks);
    this.bookmarksSubject.next(newBookmarks);
    if (this.isBrowser) {
      localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
    }
  }

  removeBookmark(bookmarkId: string) {
    const currentBookmarks = this.bookmarks();
    const newBookmarks = currentBookmarks.filter(b => b.id !== bookmarkId);
    this.bookmarks.set(newBookmarks);
    this.bookmarksSubject.next(newBookmarks);
    if (this.isBrowser) {
      localStorage.setItem('bookmarks', JSON.stringify(newBookmarks));
    }
  }

  // Note management
  addNote(note: Note) {
    const currentNotes = this.notes();
    const newNotes = [...currentNotes, note];
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    if (this.isBrowser) {
      localStorage.setItem('notes', JSON.stringify(newNotes));
    }
  }

  updateNote(noteId: string, content: string) {
    const currentNotes = this.notes();
    const newNotes = currentNotes.map(note =>
      note.id === noteId ? { ...note, content, timestamp: new Date() } : note
    );
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    if (this.isBrowser) {
      localStorage.setItem('notes', JSON.stringify(newNotes));
    }
  }

  deleteNote(noteId: string) {
    const currentNotes = this.notes();
    const newNotes = currentNotes.filter(n => n.id !== noteId);
    this.notes.set(newNotes);
    this.notesSubject.next(newNotes);
    if (this.isBrowser) {
      localStorage.setItem('notes', JSON.stringify(newNotes));
    }
  }

  // User preferences
  updateUserPreferences(preferences: Partial<UserPreferences>) {
    const currentPreferences = this.userPreferences();
    const newPreferences = { ...currentPreferences, ...preferences };
    this.userPreferences.set(newPreferences);
    this.userPreferencesSubject.next(newPreferences);
    if (this.isBrowser) {
      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));
    }
  }

  // Navigation
  goToNextPage() {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages) {
      const nextPageIndex = currentSubTopic.currentPage;
      if (nextPageIndex < currentSubTopic.pages.length) {
        currentSubTopic.currentPage = nextPageIndex + 1;
        this.selectPage(currentSubTopic.pages[nextPageIndex]);
      }
    }
  }

  goToPreviousPage() {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages) {
      const prevPageIndex = currentSubTopic.currentPage - 2;
      if (prevPageIndex >= 0) {
        currentSubTopic.currentPage = prevPageIndex + 1;
        this.selectPage(currentSubTopic.pages[prevPageIndex]);
      }
    }
  }

  goToPage(pageNumber: number) {
    const currentSubTopic = this.selectedSubTopic();
    if (currentSubTopic && currentSubTopic.pages && pageNumber > 0 && pageNumber <= currentSubTopic.pages.length) {
      currentSubTopic.currentPage = pageNumber;
      this.selectPage(currentSubTopic.pages[pageNumber - 1]);
    }
  }
}
